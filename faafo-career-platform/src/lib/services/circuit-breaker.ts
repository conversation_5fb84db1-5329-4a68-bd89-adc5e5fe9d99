/**
 * Circuit Breaker utility for external service calls
 * Extracted from geminiService.ts for better organization
 */

import { CircuitBreakerState } from './ai-service-types';

export class CircuitBreaker {
  private state: CircuitBreakerState = {
    isOpen: false,
    failureCount: 0,
    lastFailureTime: 0,
    successCount: 0
  };

  private readonly failureThreshold: number;
  private readonly recoveryTimeout: number;

  constructor(
    failureThreshold: number = 5,
    recoveryTimeout: number = 60000 // 1 minute
  ) {
    this.failureThreshold = failureThreshold;
    this.recoveryTimeout = recoveryTimeout;
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state.isOpen) {
      if (Date.now() - this.state.lastFailureTime > this.recoveryTimeout) {
        this.state.isOpen = false;
        this.state.failureCount = 0;
      } else {
        throw new Error('Circuit breaker is open - service temporarily unavailable');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.state.successCount++;
    this.state.failureCount = 0;
    this.state.isOpen = false;
  }

  private onFailure(): void {
    this.state.failureCount++;
    this.state.lastFailureTime = Date.now();
    
    if (this.state.failureCount >= this.failureThreshold) {
      this.state.isOpen = true;
    }
  }

  getState(): CircuitBreakerState {
    return { ...this.state };
  }

  reset(): void {
    this.state = {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: 0,
      successCount: 0
    };
  }

  isOpen(): boolean {
    return this.state.isOpen;
  }

  getFailureCount(): number {
    return this.state.failureCount;
  }

  getSuccessCount(): number {
    return this.state.successCount;
  }
}

// Default circuit breaker instance for AI services
export const geminiCircuitBreaker = new CircuitBreaker();
