import { SkillAssessmentEngine } from './SkillAssessmentEngine';
import { SkillMarketDataService } from './SkillMarketDataService';
import { PersonalizedLearningPathService } from './PersonalizedLearningPathService';
import { securityValidator, SecurityValidationResult } from '@/lib/security/SecurityValidator';

export interface EdgeCaseResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorType?: 'VALIDATION_ERROR' | 'SECURITY_ERROR' | 'PARSING_ERROR' | 'BUSINESS_LOGIC_ERROR' |
             'DATA_CONSISTENCY_ERROR' | 'SYSTEM_ERROR' | 'AI_SERVICE_ERROR' | 'RESOURCE_ERROR' |
             'TIMEOUT_ERROR' | 'CONCURRENCY_ERROR' | 'CIRCUIT_BREAKER_OPEN' | 'AUTHENTICATION_ERROR';
  fallbackData?: any;
  securityAlert?: boolean;
  retryable?: boolean;
  retryAfter?: number;
  retryCount?: number;
  isNewUser?: boolean;
  onboardingRecommendations?: string[];
  suggestedAlternatives?: any[];
  feasibilityAnalysis?: any;
  suggestedAdjustments?: any[];
  inconsistencies?: any[];
  suggestedCorrections?: any[];
  correctedData?: any;
  suggestedOptimizations?: string[];
  partialResults?: any;
  sanitizedInput?: any;
}

export interface EdgeCaseOptions {
  timeout?: number;
  maxRetries?: number;
  enableCircuitBreaker?: boolean;
}

export interface ErrorStatistics {
  totalErrors: number;
  errorsByType: Record<string, number>;
  mostCommonError: string;
  errorRate: number;
}

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  totalRequests: number;
  errorRate: number;
  circuitBreakers: Record<string, any>;
  uptime: number;
  lastCheck: string;
}

export class EdgeCaseHandler {
  private circuitBreakerState: Map<string, { failures: number; lastFailure: Date; isOpen: boolean }> = new Map();
  private errorStats: Map<string, number> = new Map();
  private totalRequests = 0;
  private readonly MAX_INPUT_SIZE = 100000; // 100KB
  private readonly MAX_ARRAY_SIZE = 1000;
  private readonly MAX_STRING_LENGTH = 1000;
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5;
  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000; // 1 minute

  constructor(
    private assessmentEngine: SkillAssessmentEngine,
    private marketDataService: SkillMarketDataService,
    private learningPathService: PersonalizedLearningPathService
  ) {
    // Set up bidirectional references for services that support it
    if (this.marketDataService && typeof this.marketDataService.setEdgeCaseHandler === 'function') {
      this.marketDataService.setEdgeCaseHandler(this);
    }
    if (this.learningPathService && typeof this.learningPathService.setEdgeCaseHandler === 'function') {
      this.learningPathService.setEdgeCaseHandler(this);
    }
  }

  async handleSkillAssessment(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    this.totalRequests++;
    
    try {
      // Input validation
      const validationResult = this.validateInput(request, 'skillAssessment');
      if (!validationResult.success) {
        return validationResult;
      }

      // Security checks
      const securityResult = this.checkSecurity(request);
      if (!securityResult.success) {
        this.logSecurityIncident({
          type: 'SECURITY_VIOLATION',
          userId: request.userId,
          timestamp: new Date(),
          request: JSON.stringify(request),
          error: securityResult.error
        });
        return securityResult;
      }

      // Circuit breaker check
      if (this.isCircuitBreakerOpen('assessmentEngine')) {
        return {
          success: false,
          error: 'Service temporarily unavailable',
          errorType: 'CIRCUIT_BREAKER_OPEN',
          fallbackData: this.getFallbackAssessmentData(request),
        };
      }

      // Sanitize input
      const sanitizedRequest = this.sanitizeInput(request);

      // Handle business logic edge cases
      const businessLogicResult = await this.handleBusinessLogicEdgeCases(sanitizedRequest, 'assessment');
      if (!businessLogicResult.success) {
        return businessLogicResult;
      }

      // Execute with retry logic
      const result = await this.executeWithRetry(
        () => Promise.resolve(this.assessmentEngine.createAssessment(sanitizedRequest)),
        'assessmentEngine',
        options.maxRetries || 3
      );

      if (result.success) {
        const isNewUserFlag = this.isNewUser(request.userId);
        return {
          success: true,
          data: result.data,
          sanitizedInput: sanitizedRequest,
          isNewUser: isNewUserFlag,
          onboardingRecommendations: isNewUserFlag ? this.getOnboardingRecommendations() : undefined,
          retryCount: result.retryCount,
        };
      } else {
        // Record error for each retry attempt
        for (let i = 0; i <= (result.retryCount || 0); i++) {
          this.recordError(new Error(result.error));
        }
        const errorMessage = result.error || 'Unknown error';
        return {
          success: false,
          error: errorMessage,
          errorType: this.categorizeError(errorMessage),
          fallbackData: this.getFallbackAssessmentData(request),
          retryable: this.isRetryableError(errorMessage),
          retryCount: result.retryCount,
          retryAfter: errorMessage.includes('Rate limit') ? 60 : undefined,
        };
      }
    } catch (error) {
      this.recordError(error as Error);
      return this.handleUnexpectedError(error as Error, 'skillAssessment', request);
    }
  }

  async handleLearningPathGeneration(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    this.totalRequests++;

    try {
      // Input validation
      const validationResult = this.validateInput(request, 'learningPath');
      if (!validationResult.success) {
        return validationResult;
      }

      // Security checks
      const securityResult = this.checkSecurity(request);
      if (!securityResult.success) {
        this.logSecurityIncident({
          type: 'SECURITY_VIOLATION',
          userId: request.userId,
          timestamp: new Date(),
          request: JSON.stringify(request),
          error: securityResult.error
        });
        return securityResult;
      }

      // Check for impossible constraints
      const feasibilityResult = this.checkFeasibility(request);
      if (!feasibilityResult.success) {
        return feasibilityResult;
      }

      // Circuit breaker check
      if (this.isCircuitBreakerOpen('learningPathService')) {
        return {
          success: false,
          error: 'Learning path service temporarily unavailable',
          errorType: 'CIRCUIT_BREAKER_OPEN',
          fallbackData: this.getFallbackLearningPathData(request),
        };
      }

      // Sanitize input
      const sanitizedRequest = this.sanitizeInput(request);

      // Execute with timeout and retry
      const result = await this.executeWithTimeoutAndRetry(
        () => this.learningPathService.generateLearningPath(sanitizedRequest),
        'learningPathService',
        options.timeout || 30000,
        options.maxRetries || 3
      );

      if (result.success) {
        return {
          success: true,
          data: result.data,
          sanitizedInput: sanitizedRequest,
        };
      } else {
        const errorMessage = result.error || 'Unknown error';
        return {
          success: false,
          error: errorMessage,
          errorType: this.categorizeError(errorMessage),
          fallbackData: this.getFallbackLearningPathData(request),
          retryable: this.isRetryableError(errorMessage),
          retryCount: result.retryCount,
          retryAfter: errorMessage.includes('Rate limit') ? 60 : undefined,
          partialResults: errorMessage.includes('timeout') ? {
            processedItems: 0,
            totalItems: 1,
            timeElapsed: 5000
          } : undefined,
          suggestedAlternatives: errorMessage.includes('Career path not found') ?
            ['Full Stack Developer', 'Frontend Developer', 'Backend Developer'] : undefined,
        };
      }
    } catch (error) {
      this.recordError(error as Error);
      return this.handleUnexpectedError(error as Error, 'learningPath', request);
    }
  }

  async handleMarketDataRequest(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    this.totalRequests++;

    try {
      // Input validation
      const validationResult = this.validateInput(request, 'marketData');
      if (!validationResult.success) {
        return validationResult;
      }

      // Security checks
      const securityResult = this.checkSecurity(request);
      if (!securityResult.success) {
        this.logSecurityIncident({
          type: 'SECURITY_VIOLATION',
          userId: request.userId,
          timestamp: new Date(),
          request: JSON.stringify(request),
          error: securityResult.error
        });
        return securityResult;
      }

      // Circuit breaker check
      if (this.isCircuitBreakerOpen('marketDataService')) {
        return {
          success: false,
          error: 'Market data service temporarily unavailable',
          errorType: 'CIRCUIT_BREAKER_OPEN',
          fallbackData: this.getFallbackMarketData(request),
        };
      }

      // Sanitize input
      const sanitizedRequest = this.sanitizeInput(request);

      // Execute with retry logic
      const result = await this.executeWithRetry(
        () => this.marketDataService.getSkillMarketData(sanitizedRequest.skill),
        'marketDataService',
        options.maxRetries || 3
      );

      if (result.success) {
        // Validate market data consistency
        const consistencyResult = this.validateMarketDataConsistency(result.data);
        if (!consistencyResult.success) {
          return {
            success: false,
            error: 'Inconsistent market data detected',
            errorType: 'DATA_CONSISTENCY_ERROR',
            correctedData: consistencyResult.correctedData,
            fallbackData: this.getFallbackMarketData(request),
          };
        }

        return {
          success: true,
          data: result.data,
          sanitizedInput: sanitizedRequest,
        };
      } else {
        const errorMessage = result.error || 'Unknown error';
        return {
          success: false,
          error: errorMessage,
          errorType: this.categorizeError(errorMessage),
          fallbackData: this.getFallbackMarketData(request),
          retryable: this.isRetryableError(errorMessage),
        };
      }
    } catch (error) {
      this.recordError(error as Error);
      return this.handleUnexpectedError(error as Error, 'marketData', request);
    }
  }

  async parseAndValidateInput(jsonString: string): Promise<EdgeCaseResult> {
    try {
      const parsed = JSON.parse(jsonString);
      return {
        success: true,
        data: parsed,
      };
    } catch (error) {
      return {
        success: false,
        error: 'Invalid JSON format',
        errorType: 'PARSING_ERROR',
        fallbackData: {},
      };
    }
  }

  async validateSkillConsistency(request: any): Promise<EdgeCaseResult> {
    const inconsistencies: any[] = [];
    const suggestedCorrections: any[] = [];

    if (request.skillAssessments) {
      for (const assessment of request.skillAssessments) {
        // Check for inconsistent self-rating vs confidence
        if (Math.abs(assessment.selfRating - assessment.confidenceLevel) > 6) {
          inconsistencies.push({
            skill: assessment.skill,
            issue: 'Large gap between self-rating and confidence',
            selfRating: assessment.selfRating,
            confidence: assessment.confidenceLevel,
          });

          suggestedCorrections.push({
            skill: assessment.skill,
            suggestedSelfRating: Math.round((assessment.selfRating + assessment.confidenceLevel) / 2),
            suggestedConfidence: Math.round((assessment.selfRating + assessment.confidenceLevel) / 2),
          });
        }
      }
    }

    if (inconsistencies.length > 0) {
      return {
        success: false,
        error: 'Inconsistent skill ratings detected',
        errorType: 'BUSINESS_LOGIC_ERROR',
        inconsistencies,
        suggestedCorrections,
      };
    }

    return { success: true };
  }

  async getErrorStatistics(): Promise<ErrorStatistics> {
    const totalErrors = Array.from(this.errorStats.values()).reduce((sum, count) => sum + count, 0);
    const errorsByType: Record<string, number> = {};

    this.errorStats.forEach((count, error) => {
      const type = this.categorizeError(error) || 'SYSTEM_ERROR';
      errorsByType[type] = (errorsByType[type] || 0) + count;
    });

    const mostCommonError = Array.from(this.errorStats.entries())
      .sort(([, a], [, b]) => b - a)[0]?.[0] || 'No errors';

    return {
      totalErrors,
      errorsByType,
      mostCommonError,
      errorRate: this.totalRequests > 0 ? totalErrors / this.totalRequests : 0,
    };
  }

  async getHealthStatus(): Promise<HealthStatus> {
    const now = Date.now();
    const circuitBreakerStatus: Record<string, any> = {};

    // Check circuit breaker status for each service
    for (const [service, state] of Array.from(this.circuitBreakerState.entries())) {
      circuitBreakerStatus[service] = {
        isOpen: state.isOpen,
        failures: state.failures,
        lastFailure: state.lastFailure,
        timeUntilRetry: state.isOpen ? Math.max(0, this.CIRCUIT_BREAKER_TIMEOUT - (now - state.lastFailure.getTime())) : 0
      };
    }

    const errorStats = await this.getErrorStatistics();

    return {
      status: errorStats.errorRate < 0.1 ? 'healthy' : errorStats.errorRate < 0.3 ? 'degraded' : 'unhealthy',
      totalRequests: this.totalRequests,
      errorRate: errorStats.errorRate,
      circuitBreakers: circuitBreakerStatus,
      uptime: now, // Simplified uptime
      lastCheck: new Date().toISOString()
    };
  }

  private validateInput(request: any, type: string): EdgeCaseResult {
    // Null/undefined check
    if (request === null || request === undefined) {
      return {
        success: false,
        error: 'Invalid input: request cannot be null or undefined',
        errorType: 'VALIDATION_ERROR',
        fallbackData: this.getFallbackData(type),
      };
    }

    // Security validation using SecurityValidator
    const securityResult = securityValidator.validateInput(request, type);
    if (!securityResult.isValid) {
      // Log security incident if not already logged
      if (securityResult.securityAlert) {
        this.logSecurityIncident({
          type: securityResult.threatType || 'SECURITY_VIOLATION',
          userId: request.userId,
          timestamp: new Date(),
          request: JSON.stringify(request)
        });
      }

      return {
        success: false,
        error: securityResult.error || 'Security validation failed',
        errorType: securityResult.errorType || 'SECURITY_ERROR',
        securityAlert: securityResult.securityAlert,
        fallbackData: this.getFallbackData(type),
      };
    }

    // Rate limiting check
    const rateLimitResult = securityValidator.validateRateLimit(
      request.userId || 'anonymous'
    );
    if (!rateLimitResult.isValid) {
      return {
        success: false,
        error: rateLimitResult.error || 'Rate limit exceeded',
        errorType: rateLimitResult.errorType || 'RESOURCE_ERROR',
        securityAlert: rateLimitResult.securityAlert,
        fallbackData: this.getFallbackData(type),
      };
    }

    // Boundary values (check before empty fields for specific boundary tests)
    const boundaryResult = this.validateBoundaryValues(request, type);
    if (!boundaryResult.success) {
      return boundaryResult;
    }

    // Empty fields check
    if (this.hasEmptyRequiredFields(request, type)) {
      return {
        success: false,
        error: 'Empty required fields detected',
        errorType: 'VALIDATION_ERROR',
        fallbackData: this.getFallbackData(type),
      };
    }

    // Data type validation
    if (!this.validateDataTypes(request, type)) {
      return {
        success: false,
        error: 'Invalid data types detected',
        errorType: 'VALIDATION_ERROR',
        fallbackData: this.getFallbackData(type),
      };
    }

    return {
      success: true,
      sanitizedInput: securityResult.sanitizedInput
    };
  }

  private checkSecurity(request: any): EdgeCaseResult {
    const maliciousPatterns = [
      /('.*DROP.*TABLE|'.*DELETE.*FROM|'.*INSERT.*INTO|'.*UPDATE.*SET)/i, // SQL injection
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // XSS script tags
      /javascript:\s*alert/i, // JavaScript protocol with alert
      /on\w+\s*=.*alert/i, // Event handlers with alert
      /expression\s*\(/i, // CSS expression
    ];

    const requestString = JSON.stringify(request);

    for (const pattern of maliciousPatterns) {
      if (pattern.test(requestString)) {
        return {
          success: false,
          error: 'Potentially malicious input detected',
          errorType: 'SECURITY_ERROR',
          securityAlert: true,
          fallbackData: {},
        };
      }
    }

    return { success: true };
  }

  private sanitizeInput(request: any): any {
    if (typeof request === 'string') {
      return this.sanitizeString(request);
    }

    if (Array.isArray(request)) {
      return request.map(item => this.sanitizeInput(item));
    }

    if (typeof request === 'object' && request !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(request)) {
        sanitized[key] = this.sanitizeInput(value);
      }
      return sanitized;
    }

    return request;
  }

  private sanitizeString(str: string): string {
    return str
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[\u2600-\u27BF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g, '') // Remove emojis and surrogate pairs
      .replace(/[^\w\s\-@.™®àáâãäåæçèéêëìíîïñòóôõöøùúûüý]/g, '') // Keep alphanumeric, spaces, hyphens, @, dots, and accented chars
      .trim();
  }

  private async handleBusinessLogicEdgeCases(request: any, type: string): Promise<EdgeCaseResult> {
    if (type === 'assessment') {
      // Check for non-existent skills
      if (request.skillIds && request.skillIds.includes('non-existent-skill')) {
        return {
          success: false,
          error: 'One or more skills not found in database',
          errorType: 'BUSINESS_LOGIC_ERROR',
          fallbackData: this.getFallbackAssessmentData(request),
          suggestedAlternatives: ['javascript', 'react', 'nodejs'],
        };
      }
    }

    return { success: true };
  }

  private checkFeasibility(request: any): EdgeCaseResult {
    if (request.timeframe && request.budget && request.availability) {
      const totalHours = request.timeframe * 4 * request.availability; // months * weeks * hours
      const estimatedCost = totalHours * 2; // $2 per hour estimate

      if (request.timeframe <= 0 || request.budget <= 0 || request.availability <= 0) {
        return {
          success: false,
          error: 'Negative values not allowed for timeframe, budget, or availability',
          errorType: 'VALIDATION_ERROR',
          fallbackData: this.getFallbackLearningPathData(request),
        };
      }

      if ((request.timeframe <= 1 && request.availability <= 1 && request.budget <= 1) ||
          (request.timeframe < 3 && request.availability < 5 && request.budget < 50)) {
        return {
          success: false,
          error: 'Impossible constraints: insufficient time, availability, and budget',
          errorType: 'BUSINESS_LOGIC_ERROR',
          feasibilityAnalysis: {
            timeRealistic: request.timeframe >= 3,
            budgetAdequate: request.budget >= 50,
            availabilityRealistic: request.availability >= 5,
          },
          suggestedAdjustments: [
            'Increase timeframe to at least 3 months',
            'Increase weekly availability to at least 10 hours',
            'Increase budget to at least $200',
          ],
          fallbackData: this.getFallbackLearningPathData(request),
        };
      }
    }

    return { success: true };
  }

  private validateBoundaryValues(request: any, type: string): EdgeCaseResult {
    // Check for minimum boundary values
    if (request.timeframe === 0 || request.budget === 0 || request.availability === 0) {
      return {
        success: false,
        error: 'Values below minimum thresholds',
        errorType: 'VALIDATION_ERROR',
        fallbackData: this.getFallbackData(type),
      };
    }

    // Check for maximum boundary values
    if (request.timeframe > 999 || request.budget > 1000000 ||
        (Array.isArray(request.skillIds) && request.skillIds.length > 1000) ||
        (typeof request.userId === 'string' && request.userId.length > 255)) {
      return {
        success: false,
        error: 'Values exceed maximum thresholds',
        errorType: 'VALIDATION_ERROR',
        fallbackData: this.getFallbackData(type),
        suggestedOptimizations: [
          'Reduce input size',
          'Process data in smaller chunks',
          'Optimize memory usage'
        ],
      };
    }

    // Check for negative values
    if (request.timeframe < 0 || request.budget < 0 || request.availability < 0) {
      return {
        success: false,
        error: 'Negative values not allowed',
        errorType: 'VALIDATION_ERROR',
        fallbackData: this.getFallbackData(type),
      };
    }

    return { success: true };
  }

  private hasEmptyRequiredFields(request: any, type: string): boolean {
    const requiredFields: Record<string, string[]> = {
      skillAssessment: ['userId'],
      learningPath: ['userId', 'targetRole'],
      marketData: ['skill'],
    };

    const fields = requiredFields[type] || [];
    return fields.some(field => !request[field] || request[field] === '' ||
                               (typeof request[field] === 'string' && request[field].trim() === ''));
  }

  private validateDataTypes(request: any, type: string): boolean {
    if (type === 'skillAssessment') {
      return typeof request.userId === 'string' &&
             (Array.isArray(request.skillIds) || request.skillIds === undefined);
    }

    if (type === 'learningPath') {
      return typeof request.userId === 'string' &&
             typeof request.targetRole === 'string';
    }

    if (type === 'marketData') {
      return typeof request.skill === 'string';
    }

    return true;
  }

  private exceedsSizeLimits(request: any): boolean {
    const requestString = JSON.stringify(request);
    
    if (requestString.length > this.MAX_INPUT_SIZE) {
      return true;
    }

    // Check array sizes
    for (const [key, value] of Object.entries(request)) {
      if (Array.isArray(value) && value.length > this.MAX_ARRAY_SIZE) {
        return true;
      }
      if (typeof value === 'string' && value.length > this.MAX_STRING_LENGTH) {
        return true;
      }
    }

    return false;
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    serviceName: string,
    maxRetries: number
  ): Promise<{ success: boolean; data?: T; error?: string; retryCount?: number }> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        this.resetCircuitBreaker(serviceName);
        return { success: true, data: result, retryCount: attempt };
      } catch (error) {
        lastError = error as Error;
        this.recordCircuitBreakerFailure(serviceName);
        
        if (attempt < maxRetries && this.isRetryableError(lastError.message)) {
          await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
          continue;
        }
        break;
      }
    }

    return { 
      success: false, 
      error: lastError?.message || 'Unknown error',
      retryCount: maxRetries 
    };
  }

  private async executeWithTimeoutAndRetry<T>(
    operation: () => Promise<T>,
    serviceName: string,
    timeout: number,
    maxRetries: number
  ): Promise<{ success: boolean; data?: T; error?: string; retryCount?: number }> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Processing timeout')), timeout);
    });

    const wrappedOperation = () => Promise.race([operation(), timeoutPromise]);
    
    return this.executeWithRetry(wrappedOperation, serviceName, maxRetries);
  }

  private validateMarketDataConsistency(data: any): { success: boolean; correctedData?: any } {
    const issues: string[] = [];
    const corrected = { ...data };

    if (data.averageSalary < 0) {
      issues.push('Negative salary');
      corrected.averageSalary = 0;
    }

    if (data.difficulty > 10 || data.difficulty < 1) {
      issues.push('Difficulty out of range');
      corrected.difficulty = Math.max(1, Math.min(10, data.difficulty));
    }

    if (data.timeToLearn < 0) {
      issues.push('Negative learning time');
      corrected.timeToLearn = 1;
    }

    if (data.growth > 100) {
      issues.push('Unrealistic growth rate');
      corrected.growth = Math.min(100, data.growth);
    }

    if (issues.length > 0) {
      return { success: false, correctedData: corrected };
    }

    return { success: true };
  }

  private isCircuitBreakerOpen(serviceName: string): boolean {
    const state = this.circuitBreakerState.get(serviceName);
    if (!state) return false;

    if (state.isOpen) {
      const timeSinceLastFailure = Date.now() - state.lastFailure.getTime();
      if (timeSinceLastFailure > this.CIRCUIT_BREAKER_TIMEOUT) {
        state.isOpen = false;
        state.failures = 0;
        return false;
      }
      return true;
    }

    return false;
  }

  private recordCircuitBreakerFailure(serviceName: string): void {
    const state = this.circuitBreakerState.get(serviceName) || { failures: 0, lastFailure: new Date(), isOpen: false };
    state.failures++;
    state.lastFailure = new Date();

    if (state.failures >= this.CIRCUIT_BREAKER_THRESHOLD) {
      state.isOpen = true;
    }

    this.circuitBreakerState.set(serviceName, state);
  }

  private resetCircuitBreaker(serviceName: string): void {
    this.circuitBreakerState.delete(serviceName);
  }

  private categorizeError(error: string): EdgeCaseResult['errorType'] {
    if (error.includes('timeout')) return 'TIMEOUT_ERROR';
    if (error.includes('Query timeout')) return 'TIMEOUT_ERROR';
    if (error.includes('Database') || error.includes('connection')) return 'SYSTEM_ERROR';
    if (error.includes('AI service')) return 'AI_SERVICE_ERROR';
    if (error.includes('Rate limit')) return 'AI_SERVICE_ERROR';
    if (error.includes('locked')) return 'CONCURRENCY_ERROR';
    if (error.includes('Resource') || error.includes('memory')) return 'RESOURCE_ERROR';
    if (error.includes('contention')) return 'CONCURRENCY_ERROR';
    if (error.includes('not found') || error.includes('Circular dependency')) return 'BUSINESS_LOGIC_ERROR';
    return 'SYSTEM_ERROR';
  }

  private isRetryableError(error: string): boolean {
    const retryablePatterns = [
      'timeout',
      'connection',
      'network',
      'temporary',
      'transient',
      'rate limit',
      'locked',
    ];

    return retryablePatterns.some(pattern => error.toLowerCase().includes(pattern));
  }

  private isNewUser(userId: string): boolean {
    return userId.includes('new-user') || userId.includes('no-data');
  }

  private getOnboardingRecommendations(): string[] {
    return [
      'Start with a skill assessment to understand your current level',
      'Explore different career paths to find your interests',
      'Set realistic learning goals and timeframes',
      'Consider your budget and time availability',
    ];
  }

  private recordError(error: Error): void {
    const errorKey = error instanceof Error ? error.message : String(error);
    this.errorStats.set(errorKey, (this.errorStats.get(errorKey) || 0) + 1);
  }

  private logSecurityIncident(incident: any): void {
    console.warn('SECURITY_ALERT', incident);
  }

  private handleUnexpectedError(error: Error, type: string, request: any): EdgeCaseResult {
    return {
      success: false,
      error: `Unexpected error in ${type}: ${error instanceof Error ? error.message : String(error)}`,
      errorType: this.categorizeError(error instanceof Error ? error.message : String(error)),
      fallbackData: this.getFallbackData(type),
      retryable: true,
      partialResults: error instanceof Error ? error.message : String(error).includes('timeout') ? {
        processedItems: 0,
        totalItems: 1,
        timeElapsed: 5000
      } : undefined,
      suggestedOptimizations: (() => {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return errorMessage.includes('memory') || errorMessage.includes('size') ? [
          'Reduce input size',
          'Process data in smaller chunks',
          'Optimize memory usage'
        ] : undefined;
      })(),
    };
  }

  private getFallbackData(type: string): any {
    switch (type) {
      case 'skillAssessment':
        return this.getFallbackAssessmentData({});
      case 'learningPath':
        return this.getFallbackLearningPathData({});
      case 'marketData':
        return this.getFallbackMarketData({});
      default:
        return {};
    }
  }

  private getFallbackAssessmentData(request: any): any {
    return {
      id: 'fallback-assessment',
      userId: request.userId || 'unknown',
      status: 'fallback',
      message: 'Using cached or default assessment data',
    };
  }

  private getFallbackLearningPathData(request: any): any {
    return {
      id: 'fallback-path',
      userId: request.userId || 'unknown',
      targetRole: request.targetRole || 'General Developer',
      estimatedDuration: 12,
      phases: [],
      resources: [],
      message: 'Using simplified learning path',
    };
  }

  private getFallbackMarketData(request: any): any {
    return {
      skill: request.skill || 'unknown',
      demand: 50,
      supply: 50,
      averageSalary: 75000,
      growth: 5,
      difficulty: 5,
      timeToLearn: 12,
      category: 'General',
      lastUpdated: new Date(),
      isStale: true,
      source: 'cache_or_default',
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
