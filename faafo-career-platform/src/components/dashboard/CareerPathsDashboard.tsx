import Link from 'next/link';
import React, { useEffect, useState, useCallback } from 'react';

import CareerPathCard from '@/components/career/CareerPathCard';

interface CareerPath {
  id: string;
  title: string;
  description: string;
  pros: string[];
  cons: string[];
  actionChecklist: { id: number; text: string; completed: boolean }[];
}

export default function CareerPathsDashboard() {
  const [allCareerPaths, setAllCareerPaths] = useState<CareerPath[]>([]);
  const [bookmarkedPaths, setBookmarkedPaths] = useState<string[]>([]); // Using IDs to track bookmarked paths
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCareerPaths = async () => {
      try {
        const response = await fetch('/api/career-paths');
        if (!response.ok) {
          throw new Error(`Error: ${response.statusText}`);
        }
        const data = await response.json();
        setAllCareerPaths(data);

        // Initialize bookmarked paths from local storage or mock data
        const storedBookmarks = localStorage.getItem('bookmarkedCareerPaths');
        if (storedBookmarks) {
          setBookmarkedPaths(JSON.parse(storedBookmarks));
        } else {
          // For demonstration, let's bookmark 'strategic-switcher' by default if no stored data
          setBookmarkedPaths(['strategic-switcher']);
        }

      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err instanceof Error ? err.message : String(err));
        } else {
          setError('An unknown error occurred while fetching career paths.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCareerPaths();
  }, []);

  const handleToggleBookmark = useCallback(async (pathId: string, isCurrentlyBookmarked: boolean) => {
    const newBookmarkedStatus = !isCurrentlyBookmarked;

    // Optimistically update UI
    setBookmarkedPaths(prev => 
      newBookmarkedStatus 
        ? [...prev, pathId] 
        : prev.filter(id => id !== pathId)
    );

    try {
      const response = await fetch('/api/career-paths', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bookmark',
          pathId: pathId,
          isBookmarked: newBookmarkedStatus,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error toggling bookmark: ${response.statusText}`);
      }
      // Persist to local storage
      const updatedBookmarks = newBookmarkedStatus 
        ? [...bookmarkedPaths, pathId] 
        : bookmarkedPaths.filter(id => id !== pathId);
      localStorage.setItem('bookmarkedCareerPaths', JSON.stringify(updatedBookmarks));

      console.log(`Career path ${pathId} bookmark status set to ${newBookmarkedStatus}`);
    } catch (err) {
      console.error('Failed to toggle bookmark:', err);
      // Revert UI on error
      setBookmarkedPaths(prev => 
        isCurrentlyBookmarked 
          ? [...prev, pathId] 
          : prev.filter(id => id !== pathId)
      );
      alert('Failed to update bookmark status. Please try again.');
    }
  }, [bookmarkedPaths]);

  if (loading) {
    return <div className="text-center py-8">Loading dashboard...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">Error: {error}</div>;
  }

  const displayedPaths = allCareerPaths.filter(path => bookmarkedPaths.includes(path.id));

  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-3xl font-bold tracking-tight mb-6">Your Bookmarked Career Paths</h1>
      
      {displayedPaths.length === 0 ? (
        <div className="text-center text-muted-foreground">
          <p className="mb-4">You haven&apos;t bookmarked any career paths yet.</p>
          <Link href="/career-paths">
            <Button>Explore Career Paths</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayedPaths.map((path) => {
            const isBookmarked = bookmarkedPaths.includes(path.id);
            return (
              <div key={path.id} className="relative">
                <CareerPathCard
                  title={path.title}
                  overview={path.description}
                  pros={path.pros}
                  cons={path.cons}
                  actionableSteps={path.actionChecklist.map(item => item.text)}
                  onClick={() => window.location.href = `/career-paths/${path.id}`}
                  className="h-full"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-3 right-3 text-foreground/60 hover:text-primary"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click
                    handleToggleBookmark(path.id, isBookmarked);
                  }}
                >
                  {isBookmarked ? (
                    <BookmarkCheck className="h-5 w-5 text-primary-500 fill-primary-500" />
                  ) : (
                    <Bookmark className="h-5 w-5" />
                  )}
                </Button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
} 