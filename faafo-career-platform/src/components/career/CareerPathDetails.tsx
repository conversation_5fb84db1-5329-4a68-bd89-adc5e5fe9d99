interface CareerPathDetailsProps {
  title: string
  description: string
  pros: string[]
  cons: string[]
  actionItems: {
    id: number
    text: string
    completed: boolean
  }[]
  onToggleActionItem: (id: number) => void
  onApply: () => void
  onSaveForLater: () => void
}

export default function CareerPathDetails({
  title,
  description,
  pros,
  cons,
  actionItems,
  onToggleActionItem,
  onApply,
  onSaveForLater,
}: CareerPathDetailsProps) {
  return (
    <div className="container max-w-4xl py-8">
      <div className="mb-6">
        <Link 
          href="/career-paths" 
          className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Career Paths
        </Link>
      </div>

      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight mb-3">
            {encodeHtml(title)}
          </h1>
          <p className="text-muted-foreground">
            <SafeText text={description} maxLength={1000} />
          </p>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Badge variant="secondary" className="mr-2 bg-green-50 text-green-500 hover:bg-green-100 hover:text-green-600">Pros</Badge>
                  Advantages
                </h3>
                <ul className="space-y-2">
                  {pros.map((pro, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 shrink-0" />
                      <span>{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Badge variant="secondary" className="mr-2 bg-red-50 text-red-500 hover:bg-red-100 hover:text-red-600">Cons</Badge>
                  Challenges
                </h3>
                <ul className="space-y-2">
                  {cons.map((con, index) => (
                    <li key={index} className="flex items-start">
                      <X className="h-5 w-5 text-red-500 mr-2 mt-0.5 shrink-0" />
                      <span>{con}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <div>
          <h3 className="text-xl font-semibold mb-4">Action Checklist</h3>
          <Card>
            <CardContent className="p-6">
              <ul className="space-y-3">
                {actionItems.map((item) => (
                  <li key={item.id} className="flex items-start">
                    <div 
                      className={`flex h-5 w-5 shrink-0 items-center justify-center rounded-sm border mr-3 mt-0.5 cursor-pointer ${
                        item.completed 
                          ? "bg-primary border-primary" 
                          : "border-input"
                      }`}
                      onClick={() => onToggleActionItem(item.id)}
                    >
                      {item.completed && <Check className="h-3 w-3 text-primary-foreground" />}
                    </div>
                    <span className={item.completed ? "line-through text-muted-foreground" : ""}>
                      {item.text}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        <Separator />

        <div className="flex flex-col sm:flex-row gap-4 justify-end">
          <Button variant="outline" onClick={onSaveForLater}>
            Save for Later
          </Button>
          <Button onClick={onApply}>
            Apply Now
          </Button>
        </div>
      </div>
    </div>
  )
}
