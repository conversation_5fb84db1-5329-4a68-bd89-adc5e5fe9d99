import dynamicImport from 'next/dynamic';
import { Metadata } from 'next';

import { InterviewErrorBoundary } from '@/components/common/ErrorBoundary';

// Dynamic import for the heavy interview practice component
const InterviewPracticePage = dynamicImport(() => import('@/components/interview-practice/InterviewPracticePage'), {
  loading: () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    </div>
  ),
  ssr: false
});

export const metadata: Metadata = {
  title: 'Interview Practice - FAAFO Career Platform',
  description: 'Practice common interview questions and scenarios to improve your interview skills and confidence.',
  keywords: 'interview practice, job interview, interview questions, career preparation, interview skills',
};

export default function InterviewPracticePageRoute() {
  return (
    <InterviewErrorBoundary>
      <InterviewPracticePage />
    </InterviewErrorBoundary>
  );
}
